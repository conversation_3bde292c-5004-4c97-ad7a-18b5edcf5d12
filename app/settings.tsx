import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform
} from 'react-native';

import { router, useFocusEffect } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';

import Toast from 'react-native-toast-message';

import { Storage } from 'expo-sqlite/kv-store';



export default function SettingsScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距
    const [isLoggedIn, setIsLoggedIn] = useState(false);

    const [tokenRemainingTime, setTokenRemainingTime] = useState<string>('计算中...');

    // 检查登录状态
    useEffect(() => {
        checkLoginStatus();
    }, []);

    // 当页面重新获得焦点时，重新检查登录状态
    useFocusEffect(
        React.useCallback(() => {
            checkLoginStatus();
        }, [])
    );

    // 定时器：每分钟更新一次剩余时间
    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (isLoggedIn) {
            // 立即更新一次
            updateTokenRemainingTime();
            
            // 每分钟更新一次
            interval = setInterval(() => {
                updateTokenRemainingTime();
            }, 60000); // 60秒
        }
        
        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [isLoggedIn]);

    // 检查是否已登录
    const checkIsLoggedIn = async (): Promise<boolean> => {
        try {
            const loginStatus = await Storage.getItem('isLoggedIn');
            if (loginStatus !== 'true') {
                return false;
            }
            
            // 检查token是否过期
            const isExpired = await isTokenExpired();
            if (isExpired) {
                // 如果已过期，自动清除登录状态
                await logout();
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('检查登录状态失败:', error);
            return false;
        }
    };



    // 检查token是否过期
    const isTokenExpired = async (): Promise<boolean> => {
        try {
            const expireTimeStr = await Storage.getItem('tokenExpireTime');
            if (!expireTimeStr) return true;
            
            const expireTime = new Date(expireTimeStr);
            const now = new Date();
            
            return now >= expireTime;
        } catch (error) {
            console.error('检查token过期状态失败:', error);
            return true;
        }
    };

    // 获取token剩余有效时间（毫秒）
    const getTokenRemainingTime = async (): Promise<number> => {
        try {
            const expireTimeStr = await Storage.getItem('tokenExpireTime');
            if (!expireTimeStr) return 0;
            
            const expireTime = new Date(expireTimeStr);
            const now = new Date();
            
            const remainingTime = expireTime.getTime() - now.getTime();
            return Math.max(0, remainingTime);
        } catch (error) {
            console.error('获取token剩余时间失败:', error);
            return 0;
        }
    };

    // 格式化剩余时间为可读字符串
    const formatTokenRemainingTime = async (): Promise<string> => {
        try {
            const remainingTime = await getTokenRemainingTime();
            
            if (remainingTime <= 0) {
                return '已过期';
            }
            
            const days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
            const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
            
            if (days > 0) {
                return `${days}天${hours}小时`;
            } else if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟`;
            } else {
                return '不到1分钟';
            }
        } catch (error) {
            console.error('格式化剩余时间失败:', error);
            return '未知';
        }
    };

    // 退出登录
    const logout = async (): Promise<void> => {
        try {
            await Storage.removeItem('userToken');

            await Storage.removeItem('isLoggedIn');
            await Storage.removeItem('tokenExpireTime');
            console.log('用户已退出登录');
        } catch (error) {
            console.error('退出登录失败:', error);
        }
    };

    const checkLoginStatus = async () => {
        try {
            const loggedIn = await checkIsLoggedIn();

            setIsLoggedIn(loggedIn);

            // 如果已登录，获取token剩余时间
            if (loggedIn) {
                await updateTokenRemainingTime();
            }

            console.log('登录状态:', loggedIn);
        } catch (error) {
            console.error('检查登录状态失败:', error);
        }
    };

    // 更新token剩余时间
    const updateTokenRemainingTime = async () => {
        try {
            const remainingTimeStr = await formatTokenRemainingTime();
            setTokenRemainingTime(remainingTimeStr);
            
            // 检查是否已过期
            const isExpired = await isTokenExpired();
            if (isExpired && isLoggedIn) {
                // 如果token已过期，自动退出登录
                handleLogout();
                Toast.show({
                    type: 'info',
                    text1: '登录已过期，请重新登录',
                });
            }
        } catch (error) {
            console.error('更新token剩余时间失败:', error);
            setTokenRemainingTime('未知');
        }
    };

    const handleTopLeftBack = () => {
        router.back();
    };

    // 处理退出登录
    const handleLogout = async () => {
        try {
            await logout();
            setIsLoggedIn(false);

            Toast.show({
                type: 'success',
                text1: '已退出登录',
            });

            console.log('用户已退出登录');
        } catch (error) {
            console.error('退出登录失败:', error);
            Toast.show({
                type: 'error',
                text1: '退出登录失败',
            });
        }
    };


    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> 设置 </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>

                        {/* 如果未登录，显示注册和登录按钮 */}
                        {!isLoggedIn && (
                            <>
                                <TouchableOpacity onPress={() => router.push('/login-reg')}>
                                    <View style={styles.login_reg_hang}>
                                        <Text style={styles.login_reg_hang_text}>注册</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => router.push('/login-in')}>
                                    <View style={styles.login_in_hang}>
                                        <Text style={styles.login_in_hang_text}>登录</Text>
                                    </View>
                                </TouchableOpacity>
                            </>
                        )}

                        {/* 如果已登录，显示用户信息和退出登录按钮 */}
                        {isLoggedIn && (
                            <>
                                <View style={styles.user_info_section}>
                                    <Text style={styles.user_info_title}>已登录用户</Text>
                                    <Text style={styles.user_name}>用户已登录</Text>
                                    <View style={styles.token_status_container}>
                                        <Text style={styles.token_status_label}>登录有效期剩余:</Text>
                                        <Text style={[
                                            styles.token_remaining_time,
                                            tokenRemainingTime === '已过期' ? styles.token_expired : styles.token_valid
                                        ]}>
                                            {tokenRemainingTime}
                                        </Text>
                                    </View>
                                </View>

                                <TouchableOpacity onPress={handleLogout}>
                                    <View style={styles.logout_hang}>
                                        <Text style={styles.logout_hang_text}>退出登录</Text>
                                    </View>
                                </TouchableOpacity>
                            </>
                        )}

                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',

    },

    login_in_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,
        justifyContent: 'center',

        borderRadius: 10,

        backgroundColor: 'black',
    },

    login_in_hang_text: {
        color: 'white',
    },


    login_reg_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,

        justifyContent: 'center',

        borderRadius: 10,
        backgroundColor: 'black',
    },

    login_reg_hang_text: {
        color: 'white',
    },

    // 新增样式
    user_info_section: {
        width: '100%',
        padding: 16,
        marginTop: 10,
        borderRadius: 10,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
    },

    user_info_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },

    user_name: {
        fontSize: 18,
        color: '#333',
        marginBottom: 4,
    },

    login_time: {
        fontSize: 12,
        color: '#666',
    },

    logout_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,
        justifyContent: 'center',

        borderRadius: 10,
        backgroundColor: '#ff4444',
    },

    logout_hang_text: {
        color: 'white',
    },

    // token状态相关样式
    token_status_container: {
        marginTop: 8,
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
    },

    token_status_label: {
        fontSize: 14,
        color: '#666',
        marginRight: 8,
    },

    token_remaining_time: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    token_expired: {
        color: '#ff4444',
    },

    token_valid: {
        color: '#00aa44',
    },

});
